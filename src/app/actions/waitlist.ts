"use server";

import { z } from "zod";

// Validation schema for email
const emailSchema = z.object({
  email: z.string().email("Please enter a valid email address").toLowerCase(),
});

export type WaitlistResponse = {
  success: boolean;
  message: string;
};

export async function joinWaitlist(
  formData: FormData
): Promise<WaitlistResponse> {
  try {
    // Extract and validate email
    const result = emailSchema.safeParse({
      email: formData.get("email"),
    });

    if (!result.success) {
      return {
        success: false,
        message: result.error.issues[0].message,
      };
    }

    // const { email } = result.data;

    // // Check if email already exists
    // const existingEntry = await db
    //   .select()
    //   .from(waitlist)
    //   .where(eq(waitlist.email, email))
    //   .limit(1);

    // if (existingEntry.length > 0) {
    //   return {
    //     success: false,
    //     message: "You're already on the waitlist!",
    //   };
    // }

    // // Insert new email into waitlist
    // await db.insert(waitlist).values({
    //   email,
    // });

    return {
      success: true,
      message: "Thank you for joining our waitlist! We'll be in touch soon.",
    };
  } catch (error) {
    console.error("Error adding to waitlist:", error);
    return {
      success: false,
      message: "Something went wrong. Please try again later.",
    };
  }
}
