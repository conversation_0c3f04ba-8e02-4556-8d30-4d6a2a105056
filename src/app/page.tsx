import Image from "next/image";
// import WaitlistForm from "@/components/WaitlistForm";

export default function Home() {
  return (
    <div className="font-sans bg-gradient-to-tr from-[#FCEAD8] to-[#FEF5EB] min-h-screen flex flex-col items-center justify-center p-8 text-stone-800">
      <div className="text-center max-w-[800px] w-full animate-fade-in-up flex flex-col md:flex-row md:items-center gap-4 mt-2 mb-4">
        <div className="w-60 h-60 mt-4 mx-auto rounded-3xl overflow-hidden transition-transform duration-300 flex-shrink-0">
          <Image
            src="/images/icon.webp"
            alt="Enai App"
            width={500}
            height={509}
            className="w-full h-full object-cover"
          />
        </div>

        <div className="flex flex-col text-center text-orange-950 md:text-left">
          <div className="text-2xl font-semibold text-primary mb-6 md:mb-3">
            Enai wspiera Cię w pokonaniu fobii i stresu pracując z Twoją
            podświadomością.
          </div>

          <div className="text-md text-muted-foreground text-orange-950/70 text-balance">
            Aktualnie aplikacja dostępna jest w wersji beta dla zamkniętej grupy
            użytkowników gdzie ją testujemy i usprawniamy. <br />
            Jeśli chcesz być jedną z pierwszych osób, które dowie się kiedy
            otworzymy drzwi szerzej wyślij nam maila na{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-amber-600 underline"
            >
              <EMAIL>
            </a>
            .
          </div>
        </div>

        {/* <WaitlistForm /> */}
      </div>
    </div>
  );
}
