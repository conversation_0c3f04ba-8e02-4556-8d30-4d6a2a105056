"use client";

import { useState, useTransition } from "react";
import { joinWaitlist, type WaitlistResponse } from "@/app/actions/waitlist";

export default function WaitlistForm() {
  const [isPending, startTransition] = useTransition();
  const [response, setResponse] = useState<WaitlistResponse | null>(null);

  const handleSubmit = (formData: FormData) => {
    startTransition(async () => {
      const result = await joinWaitlist(formData);
      setResponse(result);

      // Clear the form on success
      if (result.success) {
        const form = document.getElementById(
          "waitlist-form"
        ) as HTMLFormElement;
        form?.reset();
      }
    });
  };

  return (
    <div className="w-full max-w-md mx-auto space-y-4">
      <form id="waitlist-form" action={handleSubmit} className="space-y-4">
        <div className="relative">
          <input
            type="email"
            name="email"
            placeholder="Enter your email address"
            required
            disabled={isPending}
            className="w-full px-4 py-3 text-stone-700 bg-white/80 border border-stone-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:border-amber-500 transition-all duration-200 placeholder:text-stone-400 disabled:opacity-50 disabled:cursor-not-allowed"
          />
        </div>

        <button
          type="submit"
          disabled={isPending}
          className="w-full px-6 py-3 bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white font-medium rounded-lg transition-colors duration-200 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isPending ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Joining...
            </>
          ) : (
            "Join Waitlist"
          )}
        </button>
      </form>

      {response && (
        <div
          className={`p-4 rounded-lg border ${
            response.success
              ? "bg-green-50 border-green-200 text-green-800"
              : "bg-red-50 border-red-200 text-red-800"
          } animate-fade-in`}
        >
          <p className="text-sm font-medium">{response.message}</p>
        </div>
      )}
    </div>
  );
}
